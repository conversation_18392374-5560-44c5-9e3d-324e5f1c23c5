version: "3.8"

services:
  postgres:
    container_name: "qw-mono-dev-postgres"
    hostname: "qw-mono-dev-postgres"
    image: "postgres:15.3"
    ports:
      - "5432:5432"
    volumes:
      - "pgdata:/var/lib/postgresql/data"
      - "./dev_data/postgres/init.sh:/docker-entrypoint-initdb.d/init.sh"
    environment:
      POSTGRES_USER: postgres_admin
      POSTGRES_PASSWORD: postgres_admin
    labels:
      traefik.tcp.routers.postgres.entrypoints: postgres
      traefik.tcp.routers.postgres.rule: "HostSNI(`*`)"
      traefik.tcp.services.postgres.loadbalancer.server.port: 5432
      traefik.tcp.routers.postgres.tls: false
    networks: [traefik]

  minio:
    container_name: "qw-mono-dev-minio"
    hostname: "qw-mono-dev-minio"
    image: "minio/minio:RELEASE.2023-06-29T05-12-28Z"
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minio_admin
      MINIO_ROOT_PASSWORD: minio_admin
    volumes:
      - "miniodata:/data"
    labels:
      traefik.http.routers.minio.entrypoints: default
      traefik.http.routers.minio.rule: "Host(`minio.docker.localhost`)"
    networks: [traefik]
    command:
      - "server"
      - "--console-address"
      - ":9001"
      - "/data"

  keycloak:
    container_name: "qw-mono-dev-keycloak"
    hostname: "qw-mono-dev-keycloak"
    image: "quay.io/keycloak/keycloak:23.0.3-0"
    labels:
      traefik.http.routers.keycloak.entrypoints: default
      traefik.http.routers.keycloak.rule: "Host(`keycloak.docker.localhost`)"
      traefik.http.services.keycloak.loadbalancer.server.port: 8080
    networks: [traefik]
    volumes:
      - "./dev_data/keycloak/qualiwise.json:/opt/keycloak/data/import/qualiwise.json"
    environment:
      KEYCLOAK_ADMIN: keycloak_admin
      KEYCLOAK_ADMIN_PASSWORD: keycloak_admin
    command:
      - "start-dev"
      - "--health-enabled=true"
      - "--metrics-enabled=true"
      - "--import-realm"

  collabora:
    container_name: "qw-mono-dev-collabora"
    hostname: "qw-mono-dev-collabora"
    image: "collabora/code:*********.1"
    labels:
      traefik.http.routers.collabora.entrypoints: default
      traefik.http.routers.collabora.rule: "Host(`collabora.docker.localhost`)"
      traefik.http.services.collabora.loadbalancer.server.port: 9980
    networks: [traefik]
    environment:
      username: collabora_admin
      password: collabora_admin
      server_name: collabora.docker.localhost
      extra_params: "--o:ssl.enable=false"

  runtime:
    container_name: "qw-mono-dev-runtime"
    hostname: "qw-mono-dev-runtime"
    build:
      dockerfile: Dockerfile
      target: base-dev
      context: ./
    volumes:
      - ./:/home/<USER>/repo
    labels:
      # Frontend (Angular) router
      traefik.http.routers.runtime-frontend.entrypoints: default
      traefik.http.routers.runtime-frontend.rule: "Host(`app.docker.localhost`) && !PathPrefix(`/api/v1/agent`)"
      traefik.http.routers.runtime-frontend.priority: 1
      traefik.http.routers.runtime-frontend.service: runtime-frontend     # 👈 NEW
      traefik.http.services.runtime-frontend.loadbalancer.server.port: 4200
      # Backend (Falcon API) router
      traefik.http.routers.runtime-backend.entrypoints: default
      traefik.http.routers.runtime-backend.rule: "Host(`app.docker.localhost`) && PathPrefix(`/api/`) && !PathPrefix(`/api/v1/agent`)"
      traefik.http.routers.runtime-backend.priority: 50
      traefik.http.routers.runtime-backend.service: runtime-backend
      traefik.http.services.runtime-backend.loadbalancer.server.port: 8000
    networks: [traefik]
    tty: true
    working_dir: /home/<USER>/repo
    entrypoint: ["/bin/bash"]
    # platform: linux/amd64  # Use x86_64 with Rosetta 2 emulation

  rabbitmq:
    container_name: "qw-mono-dev-rabbitmq"
    hostname: "qw-mono-dev-rabbitmq"
    image: "rabbitmq:3.13-management"
    ports:
      - "5672:5672"   # AMQP protocol
      - "15672:15672" # Management UI
    volumes:
      - "rabbitmqdata:/var/lib/rabbitmq"
    environment:
      RABBITMQ_DEFAULT_USER: rabbitmq_admin
      RABBITMQ_DEFAULT_PASS: rabbitmq_admin
    networks: [traefik]
    labels:
      traefik.http.routers.rabbitmq.entrypoints: default
      traefik.http.routers.rabbitmq.rule: "Host(`rabbitmq.docker.localhost`)"
      traefik.http.services.rabbitmq.loadbalancer.server.port: 15672

  worker:
    container_name: "qw-mono-dev-worker"
    hostname: "qw-mono-dev-worker"
    build:
      dockerfile: Dockerfile
      target: build-worker
      context: ./
    depends_on:
      - rabbitmq
      - minio
    volumes:
      - ./:/home/<USER>/repo
    networks: [traefik]
    working_dir: /home/<USER>/repo
    tty: true
    entrypoint: ["/bin/bash", "qw-mono/scripts/dev_worker_entrypoint.sh"]
    labels:
      traefik.enable: "false"  # Disable Traefik routing for this service

  agent-service:
    container_name: "qw-mono-dev-agent-service"
    hostname: "qw-mono-dev-agent-service"
    build:
      dockerfile: Dockerfile
      target: build-agent-service
      context: ./
    depends_on:
      - postgres
      - runtime
    volumes:
      - ./:/home/<USER>/repo
    labels:
      traefik.http.routers.agent-service.entrypoints: default
      traefik.http.routers.agent-service.rule: "Host(`app.docker.localhost`) && PathPrefix(`/api/v1/agent`)"
      traefik.http.routers.agent-service.priority: 100
      traefik.http.services.agent-service.loadbalancer.server.port: 8000
    networks: [traefik]
    working_dir: /home/<USER>/repo
    tty: true
    entrypoint: ["/bin/bash", "qw-mono/scripts/dev_agent_entrypoint.sh"]

  mcp-server:
    container_name: "qw-mono-dev-mcp-server"
    hostname: "qw-mono-dev-mcp-server"
    build:
      dockerfile: Dockerfile
      target: build-mcp-server
      context: ./
    depends_on:
      - runtime
    ports:
      - "8000:8000" # to enable mcp inspector
    volumes:
      - ./:/home/<USER>/repo
    labels:
      traefik.enable: "true"
      traefik.http.routers.mcp-server.rule: "Host(`mcp.docker.localhost`)"
      traefik.http.routers.mcp-server.priority: "100"
      traefik.http.services.mcp-server.loadbalancer.server.port: "8000"
    networks: [traefik]
    working_dir: /home/<USER>/repo
    tty: true
    entrypoint: ["/bin/bash", "qw-mono/scripts/dev_mcp_entrypoint.sh"]

  traefik:
    container_name: "qw-mono-dev-traefik"
    hostname: "qw-mono-dev-traefik"
    image: "traefik:v2.10.5"
    ports:
      - "80:80"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command:
      - "--api.insecure=true"
      - "--providers.docker"
      - "--entryPoints.default.address=:80/tcp"
      - "--entryPoints.postgres.address=:5432/tcp"
    networks:
      traefik:
        aliases:
          - "app.docker.localhost"
          - "keycloak.docker.localhost"
          - "postgres.docker.localhost"
          - "minio.docker.localhost"
          - "collabora.docker.localhost"
          - "rabbitmq.docker.localhost"
          - "mcp.docker.localhost"

networks:
  traefik:

volumes:
  pgdata:
  miniodata:
  rabbitmqdata:
