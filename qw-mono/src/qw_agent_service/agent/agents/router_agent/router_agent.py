"""
Router agent for delegating user requests to specialized agents.
"""
from typing import Any, Dict, cast

from pydantic_ai import <PERSON>Context
from pydantic_ai.usage import Usage

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.agents.base import BaseAgent, get_agent_registry
from qw_agent_service.agent.models import AgentContext, AgentErrorResponse, AgentResponse


class RouterAgent(BaseAgent[AgentContext]):
    """Agent responsible for routing user requests to specialized agents."""

    def __init__(
        self,
        api_key: str,
        model_name: str = "gpt-4.1",
        temperature: float = 0.2,
        timeout: float = 10.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the router agent.

        Args:
            api_key: The API key for the LLM
            services: Dictionary of services that the agent can use
            model_name: The name of the LLM model to use
            temperature: The temperature for the LLM
            timeout: The timeout for the LLM
            lf: The log factory
        """
        super().__init__(
            name="router",
            description="Agent responsible for routing user requests to specialized agents",
            api_key=api_key,
            model_name=model_name,
            temperature=temperature,
            timeout=timeout,
            lf=lf,
        )

        # Initialize usage tracking
        self.usage = Usage()

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the router agent.
        """
        agent_registry = get_agent_registry(self.lf)
        agent_names = agent_registry.get_agent_names()

        return f"""
        You are the main router agent that decides which specialized agent should handle a user request.

        Available specialized agents: {', '.join(agent_names)}

        Specialized agent capabilities:

        - inspection_plan_builder_agent: Use for questions about inspection plans, PMIs
          (Product Manufacturing Information), adding items to plans, or anything related to inspection planning.

        - document_intelligence_agent: Use for ANY questions about files, documents, drawings, material certificates,
          or comparing information across different documents. This includes questions about how many files
          are available, what files exist, or any analysis of file content.

        When you receive a user request, you should:
        1. Analyze the request and the context
        2. Determine which specialized agent is best suited to handle the request based on the capabilities above
        3. ALWAYS use the delegate_to_agent tool to pass the request to the appropriate specialized agent
        4. NEVER try to answer questions yourself unless you can't match the request with any special agent.

        Always be concise, helpful, and accurate in your responses.
        """

    def _register_tools(self) -> None:
        """
        Register tools for the router agent.
        """
        # Register the delegate tool
        self.agent.tool(self._delegate_to_agent)

    async def _delegate_to_agent(self, ctx: RunContext[AgentContext], agent_name: str, prompt: str) -> AgentResponse:
        """
        Delegate a prompt to a specialized agent.
        """
        agent_registry = get_agent_registry(self.lf)
        agent = agent_registry.get_agent(agent_name)

        if not agent:
            self.logger.error(f"Agent {agent_name} not found")
            return AgentErrorResponse(error=f"Agent {agent_name} not found")

        # Log the context being passed to the specialized agent
        self.logger.info(f"Delegating prompt to agent: {agent_name}")

        # Log the context for debugging
        context_to_pass = cast(Dict[str, Any], ctx.deps)

        # Log user context if available
        if "user_context" in context_to_pass:
            user_context = context_to_pass.get("user_context", {})
            self.logger.info(f"User context keys being passed to {agent_name}: {list(user_context.keys())}")

        return await agent.process(prompt, context_to_pass, self.usage)
