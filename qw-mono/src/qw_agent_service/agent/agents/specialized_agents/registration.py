"""
Registration functions for specialized agents.
"""

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.agents.base import get_agent_registry
from qw_agent_service.agent.agents.specialized_agents.document_intelligence_agent import DocumentIntelligenceAgent
from qw_agent_service.agent.agents.specialized_agents.inspection_plan_builder_agent import InspectionPlanBuilderAgent
from qw_agent_service.agent.service_provider import ServiceProvider


def register_specialized_agents(
    api_key: str,
    service_provider: ServiceProvider,
    lf: LogFactory = NO_LOG_FACTORY,
) -> None:
    """
    Register specialized agents with the global registry.

    Args:
        api_key: The API key for the LLM
        service_provider: The service provider for accessing services
        lf: The log factory
    """
    registry = get_agent_registry(lf)

    # Register the inspection plan agent
    registry.register_agent(
        InspectionPlanBuilderAgent(
            api_key=api_key,
            lf=lf,
        )
    )

    # Register the cross document auditor agent
    registry.register_agent(
        DocumentIntelligenceAgent(
            api_key=api_key,
            service_provider=service_provider,
            lf=lf,
        )
    )
