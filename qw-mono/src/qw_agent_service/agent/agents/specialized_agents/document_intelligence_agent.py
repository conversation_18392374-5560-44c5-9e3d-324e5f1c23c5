"""
Agent specialized for cross document auditing tasks.
"""
from typing import Any, Dict

from pydantic_ai import <PERSON><PERSON>ontext

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.agents.base import BaseAgent
from qw_agent_service.agent.models import Agent<PERSON>ontext
from qw_agent_service.agent.service_provider import ServiceProvider
from qw_agent_service.agent.tools.common_tools.get_available_actions_tool import get_available_actions
from qw_agent_service.agent.tools.common_tools.get_context_tool import get_context

from qw_trunk.service.drawing.technical_drawing_analysis_client import TechnicalDrawingAnalysisClient
from qw_trunk.service.material_certificate.material_certificate import MaterialCertificateService
from qw_trunk.service.resource.file import FileResourceService
from qw_trunk.service.resource.s3_object import S3ObjectService


class DocumentIntelligenceAgent(BaseAgent[AgentContext]):
    """Agent specialized for cross document auditing tasks."""

    def __init__(
        self,
        api_key: str,
        service_provider: ServiceProvider,
        model_name: str = "gpt-4.1",
        temperature: float = 0.2,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the cross document auditor agent.

        Args:
            api_key: The API key for the LLM
            service_provider: The service provider for accessing services
            model_name: The name of the LLM model to use
            temperature: The temperature for the LLM
            timeout: The timeout for the LLM
            lf: The log factory
        """
        self._service_provider = service_provider

        super().__init__(
            name="document_intelligence_agent",
            description="Agent specialized for cross document auditing tasks",
            api_key=api_key,
            model_name=model_name,
            temperature=temperature,
            timeout=timeout,
            lf=lf,
        )

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the cross document auditor agent.
        """
        return """
        You are a specialized assistant for answering questions about files associated with a single order line.

        You can help users:
        1. Check all files associated with the current order line
        2. Retrieve analysis for files labeled as technical drawing or material certificate
        3. Compare information across different files to identify inconsistencies or relationships

        Available tools:
        - get_context: Get the current context of frontend
        - get_technical_drawing_analysis: Get technical drawing analysis for a single technical drawing.
        Requires file_resource_revision_id parameter from context
        - get_material_certificate_analysis: Get material certificate analysis for a single material certificate.
        Requires file_resource_revision_id parameter from context

        The context contains information about the current view:
        - fileResources: List of files in the current order. A file object contains details about revisions.
        - viewedFileResource: Currently viewed file resource if any.
        - orderLineId: Current order line ID
        - revisionToResourceMap: Mapping of revision IDs to file resource information. Each entry contains:
          - fileResourceId: The ID of the file resource
          - fileResourceName: The display name of the file resource
          - fileResourceLabel: The label of the file resource

        Always be concise, helpful, and accurate in your responses.
        """

    def _register_tools(self) -> None:
        """
        Register tools for the cross document auditor agent.
        """
        # Register common tools
        self.agent.tool(get_context)
        self.agent.tool(get_available_actions)

        # Import specialized tools
        from qw_trunk.service.agent.tools.special_tools.get_material_certificate_analysis_tool import (
            get_material_certificate_analysis,
        )
        from qw_trunk.service.agent.tools.special_tools.get_technical_drawing_analysis_tool import (
            get_technical_drawing_analysis,
        )

        # Create service access functions (these won't be serialized)
        def get_drawing_analysis_client() -> TechnicalDrawingAnalysisClient:
            return self._service_provider.get_drawing_analysis_client()  # type: ignore[no-any-return]

        def get_file_service() -> FileResourceService:
            return self._service_provider.get_file_service()  # type: ignore[no-any-return]

        def get_s3_service() -> S3ObjectService:
            return self._service_provider.get_s3_service()  # type: ignore[no-any-return]

        def get_material_certificate_service() -> MaterialCertificateService:
            return self._service_provider.get_material_certificate_service()  # type: ignore[no-any-return]

        # Create named wrapper functions for the tools
        async def technical_drawing_analysis_tool(ctx: RunContext[AgentContext], **kwargs: Any) -> Dict[str, Any]:
            """
            Get technical drawing analysis for a file resource revision.
            """
            return await get_technical_drawing_analysis(
                ctx,
                drawing_analysis_client=get_drawing_analysis_client(),
                file_service=get_file_service(),
                s3_service=get_s3_service(),
                **kwargs
            )

        async def material_certificate_analysis_tool(ctx: RunContext[AgentContext], **kwargs: Any) -> Dict[str, Any]:
            """
            Get material certificate analysis for a file resource revision.
            """
            return await get_material_certificate_analysis(
                ctx,
                material_certificate_service=get_material_certificate_service(),
                s3_service=get_s3_service(),
                **kwargs
            )

        # Register the named wrapper functions
        self.agent.tool(technical_drawing_analysis_tool)
        self.agent.tool(material_certificate_analysis_tool)
