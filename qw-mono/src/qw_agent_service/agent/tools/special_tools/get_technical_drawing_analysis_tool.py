"""
Tool for retrieving technical drawing analysis.
"""
import json
from typing import Any, Dict, Optional

from pydantic_ai import <PERSON><PERSON>ontext

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.models import Agent<PERSON>ontext
from qw_agent_service.agent.tools.base import InformationTool
from qw_trunk.service.drawing.technical_drawing_analysis_client import TechnicalDrawingAnalysisClient


class GetTechnicalDrawingAnalysisTool(InformationTool[AgentContext]):
    """Tool that retrieves technical drawing analysis for a file resource revision."""

    def __init__(
        self,
        drawing_analysis_client: TechnicalDrawingAnalysisClient,
        file_service: Optional[Any] = None,
        s3_service: Optional[Any] = None,
        name: str = "get_technical_drawing_analysis",
        description: str = "Get technical drawing analysis for a file resource revision",
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the tool with required services.

        Args:
            drawing_analysis_client: The client for accessing drawing analysis
            file_service: Optional file service for accessing files
            s3_service: Optional S3 service for accessing objects
            name: The name of the tool
            description: The description of the tool
            lf: The log factory
        """
        super().__init__(name, description, lf)
        self.drawing_analysis_client = drawing_analysis_client
        self.file_service = file_service
        self.s3_service = s3_service

    async def get_information(self, ctx: RunContext[AgentContext], **kwargs: Any) -> Dict[str, Any]:
        """
        Get technical drawing analysis for a file resource revision.

        Args:
            ctx: The run context
            **kwargs: Keyword arguments including:
                parameters: Dictionary containing:
                    file_resource_revision_id: Optional ID of the file resource revision to analyze
                    If not provided, will try to extract from context

        Returns:
            Dictionary containing analysis data or error information
        """
        try:
            # Extract parameters
            params = kwargs.get("parameters", {})
            file_resource_revision_id = params.get("file_resource_revision_id")

            if not file_resource_revision_id:
                return {"status": "ERROR", "error": "Missing required parameter: file_resource_revision_id"}

            # Use the service directly to get the analysis data
            self.logger.info(f"Getting drawing analysis for file_resource_revision_id: {file_resource_revision_id}")

            # Get analysis data for all pages
            # Import the DrawingAnalysisResult model to ensure consistent handling
            from qw_drawing_toolkit_ocr.post_processing.models import DrawingAnalysisResult

            # Get analysis data
            analysis_data = self.drawing_analysis_client.get_analysis_data(file_resource_revision_id)

            if not analysis_data:
                return {"status": "NOT_FOUND", "message": "No analysis found for this drawing revision"}

            # Check if S3 service is available
            if not self.s3_service:
                return {"status": "ERROR", "error": "S3 service not available"}

            # Normalize the analysis data to always treat it as multi-page
            normalized_data = {}
            if isinstance(analysis_data, tuple):
                # Convert single-page tuple to a dictionary with a single entry
                obj_id, status = analysis_data
                normalized_data[0] = (obj_id, status)
            else:
                # Already in multi-page format
                normalized_data = analysis_data

            # Initialize result structures
            combined_results: Dict[str, list[Any]] = {}
            metadata: Optional[Dict[str, Any]] = None
            all_complete = True
            has_valid_data = False

            # Process all pages in a unified way
            for page_idx, (obj_id, status) in normalized_data.items():
                if not obj_id or status != "COMPLETED":
                    all_complete = False
                    continue

                try:
                    # Get the object data from S3
                    obj_stream = self.s3_service.get_object_stream(obj_id)
                    page_data = json.load(obj_stream)

                    try:
                        # Try to create the DrawingAnalysisResult directly
                        drawing_result = DrawingAnalysisResult(**page_data)
                        has_valid_data = True

                        # Add page data to combined results
                        for classification, items in drawing_result.results.items():
                            key = f"{page_idx}_{classification}"
                            if key not in combined_results:
                                combined_results[key] = []
                            combined_results[key].extend(items)

                        # Use metadata from the first page that has it
                        if drawing_result.metadata and metadata is None:
                            # Convert Pydantic model to dict for serialization
                            metadata = drawing_result.metadata.model_dump()
                    except Exception as e:
                        # Log the error for debugging
                        self.logger.warning(f"Could not parse drawing analysis data for page {page_idx}: {str(e)}")
                        # Skip this page and continue with others
                except Exception as e:
                    self.logger.error(f"Error getting object {obj_id} from S3: {str(e)}")
                    # Continue with other pages

            # Handle processing status
            if not all_complete and not has_valid_data:
                return {"status": "PROCESSING", "message": "Analysis is still being processed for some pages"}

            # Return results, even if empty
            return {"status": "SUCCESS", "analysis": {"results": combined_results, "metadata": metadata}}

        except Exception as e:
            self.logger.error(f"Error getting technical drawing analysis: {str(e)}")
            return {"status": "ERROR", "error": str(e)}


def create_technical_drawing_analysis_tool(
    drawing_analysis_client: TechnicalDrawingAnalysisClient,
    file_service: Optional[Any] = None,
    s3_service: Optional[Any] = None,
    lf: LogFactory = NO_LOG_FACTORY,
) -> GetTechnicalDrawingAnalysisTool:
    """
    Create a new instance of the technical drawing analysis tool.

    Args:
        drawing_analysis_client: The client for accessing drawing analysis
        file_service: Optional file service for accessing files
        s3_service: Optional S3 service for accessing objects
        lf: The log factory

    Returns:
        A new GetTechnicalDrawingAnalysisTool instance
    """
    return GetTechnicalDrawingAnalysisTool(
        drawing_analysis_client=drawing_analysis_client,
        file_service=file_service,
        s3_service=s3_service,
        lf=lf,
    )


# Wrapper function for direct use with pydantic-ai Agent
async def get_technical_drawing_analysis(
    ctx: RunContext[AgentContext],
    drawing_analysis_client: Optional[Any] = None,
    file_service: Optional[Any] = None,
    s3_service: Optional[Any] = None,
    **kwargs: Any,
) -> Dict[str, Any]:
    """
    Get technical drawing analysis for a file resource revision.

    Args:
        ctx: The run context
        drawing_analysis_client: The technical drawing analysis client
        file_service: The file resource service
        s3_service: The S3 object service
        **kwargs: Keyword arguments including:
            parameters: Dictionary containing:
                file_resource_revision_id: Optional ID of the file resource revision to analyze
                If not provided, will try to extract from context

    Returns:
        Dictionary containing:
            analysis: The technical drawing analysis data (if successful)
            status: "SUCCESS", "ERROR", or status from the analysis service
            error: Error message (if status is "ERROR")
            message: Additional information (if available)
    """
    if not drawing_analysis_client:
        return {"status": "ERROR", "error": "Drawing analysis client not available"}

    tool = create_technical_drawing_analysis_tool(
        drawing_analysis_client=drawing_analysis_client,
        file_service=file_service,
        s3_service=s3_service,
        lf=NO_LOG_FACTORY,
    )
    return await tool.get_information(ctx, **kwargs)
