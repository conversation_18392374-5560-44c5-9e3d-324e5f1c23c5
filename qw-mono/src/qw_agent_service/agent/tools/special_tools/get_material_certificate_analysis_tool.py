"""
Tool for retrieving material certificate analysis.
"""
import json
from typing import Any, Dict, Optional

from pydantic_ai import <PERSON><PERSON>ontext

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.agent.models import AgentContext
from qw_agent_service.agent.tools.base import InformationTool
from qw_trunk.service.material_certificate.material_certificate import MaterialCertificateService


class GetMaterialCertificateAnalysisTool(InformationTool[AgentContext]):
    """Tool that retrieves material certificate analysis for a file resource revision."""

    def __init__(
        self,
        material_certificate_service: MaterialCertificateService,
        s3_service: Optional[Any] = None,
        name: str = "get_material_certificate_analysis",
        description: str = "Get material certificate analysis for a file resource revision",
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the tool with required services.

        Args:
            material_certificate_service: The service for accessing material certificate data
            s3_service: Optional S3 service for accessing objects
            name: The name of the tool
            description: The description of the tool
            lf: The log factory
        """
        super().__init__(name, description, lf)
        self.material_certificate_service = material_certificate_service
        self.s3_service = s3_service

    async def get_information(self, ctx: RunContext[AgentContext], **kwargs: Any) -> Dict[str, Any]:
        """
        Get material certificate analysis for a file resource revision.

        Args:
            ctx: The run context
            **kwargs: Keyword arguments including:
                parameters: Dictionary containing:
                    file_resource_revision_id: ID of the file resource revision to get analysis

        Returns:
            Dictionary containing analysis data or error information
        """
        try:
            # Extract parameters
            params = kwargs.get("parameters", {})
            file_resource_revision_id = params.get("file_resource_revision_id")

            if not file_resource_revision_id:
                return {"status": "ERROR", "error": "Missing required parameter: file_resource_revision_id"}

            # Use the service directly to get the analysis
            self.logger.info(
                f"Getting material certificate analysis for file_resource_revision_id: {file_resource_revision_id}"
            )

            # Find the analysis record
            analysis = self.material_certificate_service.db_service.find_material_certificate_analysis(
                file_resource_revision_id
            )

            if not analysis:
                return {"status": "NOT_FOUND", "message": "No analysis found for this material certificate"}

            if not analysis.analysis_obj_id:
                return {"status": "PROCESSING", "message": "Analysis is still being processed"}

            # Get the object data from S3
            if self.s3_service and analysis.analysis_obj_id:
                try:
                    obj_stream = self.s3_service.get_object_stream(analysis.analysis_obj_id)
                    analysis_data = json.load(obj_stream)

                    # Return the analysis data
                    return {"status": "SUCCESS", "analysis": analysis_data}
                except Exception as e:
                    self.logger.error(f"Error getting object from S3: {str(e)}")
                    return {"status": "ERROR", "error": f"Error getting analysis data: {str(e)}"}
            else:
                return {"status": "ERROR", "error": "S3 service not available or analysis object ID is missing"}

        except Exception as e:
            self.logger.error(f"Error getting material certificate analysis: {str(e)}")
            return {"status": "ERROR", "error": str(e)}


def create_material_certificate_analysis_tool(
    material_certificate_service: MaterialCertificateService,
    s3_service: Optional[Any] = None,
    lf: LogFactory = NO_LOG_FACTORY,
) -> GetMaterialCertificateAnalysisTool:
    """
    Create a new instance of the material certificate analysis tool.

    Args:
        material_certificate_service: The service for accessing material certificate data
        s3_service: Optional S3 service for accessing objects
        lf: The log factory

    Returns:
        A new GetMaterialCertificateAnalysisTool instance
    """
    return GetMaterialCertificateAnalysisTool(
        material_certificate_service=material_certificate_service,
        s3_service=s3_service,
        lf=lf,
    )


# Wrapper function for direct use with pydantic-ai Agent
async def get_material_certificate_analysis(
    ctx: RunContext[AgentContext],
    material_certificate_service: Optional[Any] = None,
    s3_service: Optional[Any] = None,
    **kwargs: Any,
) -> Dict[str, Any]:
    """
    Get material certificate analysis for a file resource revision.

    Args:
        ctx: The run context
        material_certificate_service: The material certificate service
        s3_service: The S3 object service
        **kwargs: Keyword arguments including:
            parameters: Dictionary containing:
                file_resource_revision_id: file_resource_revision_id: ID of the file resource revision to get analysis

    Returns:
        Dictionary containing:
            analysis: The material certificate analysis data (if successful)
            status: "SUCCESS" or "ERROR"
            error: Error message (if status is "ERROR")
    """
    if not material_certificate_service:
        return {"status": "ERROR", "error": "Material certificate service not available"}

    tool = create_material_certificate_analysis_tool(
        material_certificate_service=material_certificate_service,
        s3_service=s3_service,
        lf=NO_LOG_FACTORY,
    )
    return await tool.get_information(ctx, **kwargs)
