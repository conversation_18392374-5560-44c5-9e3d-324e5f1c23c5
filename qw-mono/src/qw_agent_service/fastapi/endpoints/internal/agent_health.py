from fastapi import APIRouter, Request
from pydantic import Field, BaseModel

from qw_agent_service.agents.service import AgentService

router = APIRouter()

class HealthResponse(BaseModel):
    """Health check response model."""

    status: str
    service: str
    version: str = Field(default="1.0.0", description="Service version")
    agent_available: bool

@router.get("/health", response_model=HealthResponse)
async def health_check(request: Request) -> HealthResponse:
    """Health check endpoint."""
    try:
        service: AgentService = request.app.state.agent_service
        return HealthResponse(
            status="healthy",
            service="agent-service",
            version="1.0.0",
            agent_available=service.agent_available
        )
    except Exception:
        return HealthResponse(
            status="unhealthy",
            service="agent-service",
            version="1.0.0",
            agent_available=False
        )
