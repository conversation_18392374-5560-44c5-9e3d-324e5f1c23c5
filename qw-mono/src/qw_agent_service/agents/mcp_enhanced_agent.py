"""MCP-enhanced RouterAgent implementation with FastMCP client integration and specialized agent delegation."""
from typing import Any, Dict, Optional, cast,

from pydantic_ai import Agent as PydanticAgent, RunContext
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage
from fastmcp import Client
import httpx

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.config import AgentServiceConfig
from qw_agent_service.agents.minimal_agent import MinimalAgentResponse

# Import specialized agents
from qw_trunk.service.agent.agents.specialized_agents.inspection_plan_builder_agent import InspectionPlanBuilderAgent
from qw_trunk.service.agent.models import AgentContext, AgentResponse, AgentActionResponse, AgentTextResponse, AgentErrorResponse
from qw_agent_service.agents.document_intelligence_mcp_agent import DocumentIntelligenceMCPAgent
from fastmcp.client.transports import StreamableHttpTransport


class MCPEnhancedAgent:
    """
    MCP-enhanced RouterAgent that can use internal API tools via FastMCP client.
    """

    def __init__(
        self,
        api_key: str,
        mcp_server_url: str,
        model_name: str = "gpt-4o",
        temperature: float = 0.2,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the MCP-enhanced router agent.
        """
        self.api_key = api_key
        # Fix URL configuration - remove /mcp suffix for streamable-http
        self.mcp_server_url = mcp_server_url.rstrip('/mcp').rstrip('/')
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout
        self.logger = lf.get_logger(__name__)
        self.lf = lf
        self.agent_available = False
        self.usage = Usage()

        # Session management
        self.session_token: Optional[str] = None

        # MCP client
        self.mcp_client: Optional[Client] = None
        self.mcp_tools_available = False
        self.available_mcp_tools = []

        # Specialized agents
        self.specialized_agents: Dict[str, Any] = {}
        self._initialize_specialized_agents()

        self._configure_agent()

    def _initialize_specialized_agents(self) -> None:
        """Initialize specialized agents."""
        try:
            # Initialize inspection plan builder agent (uses service injection)
            self.specialized_agents["inspection_plan_builder_agent"] = InspectionPlanBuilderAgent(
                api_key=self.api_key,
                model_name=self.model_name,
                temperature=self.temperature,
                timeout=self.timeout,
                lf=self.lf,
            )
            self.logger.info("Inspection planner agent initialization completed")

        except Exception as e:
            self.logger.error(f"Failed to initialize specialized agents: {e}")

    def _initialize_document_intelligence_agent(self) -> None:
        """Initialize document intelligence agent with MCP client."""
        try:


            self.specialized_agents["document_intelligence_agent"] = DocumentIntelligenceMCPAgent(
                api_key=self.api_key,
                mcp_client=self.mcp_client,
                model_name=self.model_name,
                temperature=self.temperature,
                timeout=self.timeout,
                lf=self.lf,
            )
            self.logger.info("Initialized document intelligence MCP agent")

        except Exception as e:
            self.logger.error(f"Failed to initialize document intelligence agent: {e}")

    async def _setup_mcp_client(self) -> None:
        """Setup MCP client connection with dependency check."""
        try:
            self.logger.info(f"Setting up MCP client for: {self.mcp_server_url}")

            # First check if MCP server is available
            if not await self._check_mcp_server_health():
                self.logger.warning("MCP server not available, skipping MCP client setup")
                return

            # Create FastMCP client with session token if available
            self.mcp_client = self._create_mcp_client()

            # Test connection and get available tools
            await self._discover_mcp_tools()

            # Initialize document intelligence agent with MCP client
            self._initialize_document_intelligence_agent()

            self.logger.info("MCP client setup completed successfully")

        except Exception as e:
            self.logger.error(f"Failed to setup MCP client: {e}")
            self.mcp_client = None
            self.mcp_tools_available = False

    def _create_mcp_client(self) -> Client:
        """Create MCP client with authentication headers if session token is available."""


        if self.session_token:
            # Create transport with session token in headers
            transport = StreamableHttpTransport(
                url=self.mcp_server_url,
                headers={"X-Session-Token": self.session_token}
            )
            self.logger.info(f"Created MCP client with session token: {self.session_token[:10]}...")
            return Client(transport)
        else:
            # Create client without authentication
            self.logger.info("Created MCP client without session token")
            return Client(self.mcp_server_url)

    async def _check_mcp_server_health(self) -> bool:
        """Check if MCP server is available."""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                # Try to connect to the MCP server
                response = await client.get(f"{self.mcp_server_url}/")
                # For MCP streamable-http, we expect a specific response
                return response.status_code in [200, 406]  # 406 is expected for non-MCP requests
        except Exception as e:
            self.logger.info(f"MCP server health check failed: {e}")
            return False

    async def _discover_mcp_tools(self) -> None:
        """Discover available tools from MCP server."""
        try:
            if not self.mcp_client:
                return

            # Connect to MCP server and list tools
            async with self.mcp_client:
                tools = await self.mcp_client.list_tools()
                self.logger.info(f"Discovered {len(tools)} MCP tools: {[tool.name for tool in tools]}")
                self.mcp_tools_available = len(tools) > 0

                # Store available tools for reference
                self.available_mcp_tools = [tool.name for tool in tools]

        except Exception as e:
            self.logger.error(f"Failed to discover MCP tools: {e}")
            self.mcp_tools_available = False

        # Initialize available tools list
        self.available_mcp_tools = []

    def _configure_agent(self) -> None:
        """
        Configure the pydantic-ai agent with MCP tools.
        """
        try:
            # Prepare model settings
            model_settings = ModelSettings(
                temperature=self.temperature,
                timeout=self.timeout,
            )

            # Setup OpenAI model with provider
            provider = OpenAIProvider(api_key=self.api_key)

            # Extract model name without provider prefix if needed
            model_name = self.model_name
            if model_name.startswith("openai:"):
                model_name = model_name.split(":")[-1]

            model = OpenAIModel(model_name, provider=provider)

            # Create the agent with MCP tools
            self.agent = PydanticAgent[AgentContext, Any](
                model=model,
                model_settings=model_settings,
                retries=2,
                system_prompt=self._get_system_prompt(),
                deps_type=AgentContext,
            )

            # Add MCP tools to the agent
            self._register_mcp_tools()

            self.agent_available = True
            self.logger.info("MCP-enhanced router agent configured successfully")

        except Exception as e:
            self.logger.error(f"Failed to configure MCP-enhanced router agent: {e}")
            self.agent_available = False

    def _register_mcp_tools(self) -> None:
        """Register basic tools with the pydantic-ai agent."""

        @self.agent.tool
        async def set_session_token(ctx: RunContext[AgentContext], token: str) -> str:
            """Set the session token for API authentication."""
            self.session_token = token
            self.logger.info(f"Session token set: {token[:10]}...")

            # Recreate MCP client with new session token
            if self.mcp_client:
                try:
                    self.mcp_client = self._create_mcp_client()
                    self.logger.info("MCP client updated with new session token")
                except Exception as e:
                    self.logger.error(f"Failed to update MCP client with session token: {e}")

            return f"Session token configured successfully"

        @self.agent.tool
        async def get_system_status(ctx: RunContext[AgentContext]) -> str:
            """Get the current system status."""
            status = "System is operational."
            if self.mcp_tools_available:
                status += f" MCP integration available with {len(self.available_mcp_tools)} tools."
                if self.available_mcp_tools:
                    status += f" Available tools: {', '.join(self.available_mcp_tools)}"
            else:
                status += " MCP integration configured but no tools available."
            return status

        @self.agent.tool
        async def list_available_mcp_tools(ctx: RunContext[AgentContext]) -> str:
            """List all available MCP tools from the server."""
            try:
                if not self.mcp_client:
                    return "MCP client not available"

                async with self.mcp_client:
                    tools = await self.mcp_client.list_tools()
                    if tools:
                        tool_list = []
                        for tool in tools:
                            description = tool.description or "No description available"
                            tool_list.append(f"- {tool.name}: {description}")
                        return f"Available MCP tools ({len(tools)}):\n" + "\n".join(tool_list)
                    else:
                        return "No MCP tools are currently available"

            except Exception as e:
                self.logger.error(f"Error listing MCP tools: {e}")
                return f"Error retrieving MCP tools: {str(e)}"

        @self.agent.tool
        async def call_mcp_tool(ctx: RunContext[AgentContext], tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> str:
            """Call an MCP tool by name with arguments."""
            try:
                if not self.mcp_client:
                    return f"MCP client not available"

                if not self.mcp_tools_available:
                    return f"No MCP tools are available"

                async with self.mcp_client:
                    result = await self.mcp_client.call_tool(tool_name, arguments or {})
                    # Extract content from result
                    if result and len(result) > 0:
                        content = result[0]
                        # Try to get text content, fallback to string representation
                        try:
                            if hasattr(content, 'text'):
                                return str(getattr(content, 'text', ''))
                            else:
                                return str(content)
                        except Exception:
                            return str(content)
                    return f"Tool {tool_name} executed successfully"

            except Exception as e:
                self.logger.error(f"Error calling MCP tool {tool_name}: {e}")
                return f"Error executing tool {tool_name}: {str(e)}"

        @self.agent.tool
        async def delegate_to_agent(ctx: RunContext[AgentContext], agent_name: str, prompt: str) -> AgentResponse:
            """Delegate a prompt to a specialized agent."""
            try:
                if agent_name not in self.specialized_agents:

                    return AgentErrorResponse(error=f"Agent {agent_name} not found. Available agents: {', '.join(self.specialized_agents.keys())}")

                agent = self.specialized_agents[agent_name]

                # Log the delegation
                self.logger.info(f"Delegating prompt to agent: {agent_name}")

                # Prepare context from MinimalAgentContext
                context_data = cast(Dict[str, Any], ctx.deps)

                # Log user context if available
                if "user_context" in context_data:
                    user_context = context_data.get("user_context", {})
                    self.logger.info(f"User context keys being passed to {agent_name}: {list(user_context.keys())}")

                # Convert MinimalAgentContext to AgentContext for specialized agents
                agent_context: AgentContext = {
                    "session_id": context_data.get("session_id", ""),
                    "user_context": context_data.get("user_context", {}),
                    "available_actions": context_data.get("available_actions", [])
                }

                # Call the appropriate agent based on type
                if agent_name == "inspection_plan_builder_agent":
                    # This agent uses the old interface with usage parameter
                    response = await agent.process(prompt, agent_context, self.usage)
                    return response

                elif agent_name == "document_intelligence_agent":
                    # This agent uses the new interface without usage parameter
                    response = await agent.process(prompt, agent_context)
                    return response

                else:

                    return AgentErrorResponse(error=f"Unknown agent type: {agent_name}")

            except Exception as e:
                self.logger.error(f"Error delegating to agent {agent_name}: {e}")
                return AgentErrorResponse(error=f"Error delegating to agent {agent_name}: {str(e)}")

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the MCP-enhanced router agent.
        """
        available_agents = list(self.specialized_agents.keys())

        return f"""
        You are the main router agent that decides which specialized agent should handle a user request.

        Available specialized agents: {', '.join(available_agents)}

        Specialized agent capabilities:

        - inspection_plan_builder_agent: Use for questions about inspection plans, PMIs
          (Product Manufacturing Information), adding items to plans, or anything related to inspection planning.

        - document_intelligence_agent: Use for ANY questions about files, documents, drawings, material certificates,
          or comparing information across different documents. This includes questions about how many files
          are available, what files exist, or any analysis of file content.

        When you receive a user request, you should:
        1. Analyze the request and the context
        2. Determine which specialized agent is best suited to handle the request based on the capabilities above
        3. ALWAYS use the delegate_to_agent tool to pass the request to the appropriate specialized agent
        4. NEVER try to answer questions yourself unless you can't match the request with any special agent.

        You also have access to these tools for system management:
        - set_session_token: Set authentication token for API access
        - get_system_status: Check system and MCP integration status
        - list_available_mcp_tools: Get a detailed list of all available MCP tools
        - call_mcp_tool: Call any available MCP tool by name with arguments
        - delegate_to_agent: Delegate a request to a specialized agent

        Always be concise, helpful, and accurate in your responses.
        """

    async def set_session_token(self, token: str) -> None:
        """Set session token for API authentication."""
        self.session_token = token
        self.logger.info(f"Session token set for agent: {token[:10]}...")

    async def process(
        self,
        prompt: str,
        context: Optional[Dict[str, Any]] = None,
        usage: Optional[Usage] = None,
    ) -> MinimalAgentResponse:
        """
        Process a user prompt and return a response with MCP tool access.
        """
        if not self.agent_available:
            self.logger.error("Agent not available for processing")
            return MinimalAgentResponse(
                message="I'm currently unavailable. Please try again later.",
                actions=[]
            )

        # Setup MCP client if not already done (but don't block if server unavailable)
        if not self.mcp_client:
            await self._setup_mcp_client()

        try:
            # Convert MinimalAgentContext to AgentContext
            context_data = context or {}

            agent_context: AgentContext = {
                "session_id": context_data.get("session_id", ""),
                "user_context": context_data.get("user_context", {}),
                "available_actions": context_data.get("available_actions", [])
            }

            # Run the agent with MCP tools available
            result = await self.agent.run(prompt, deps=agent_context, usage=usage)

            output = result.output

            # 1) Forward a MinimalAgentResponse untouched
            if isinstance(output, MinimalAgentResponse):
                self.logger.info("Path 1")
                return output

            # 2) Convert an AgentResponse → MinimalAgentResponse
            if isinstance(output, AgentResponse):


                if isinstance(output, AgentActionResponse):
                    self.logger.info("Path 2a")
                    return MinimalAgentResponse(
                        message=output.message,
                        actions=output.actions,
                    )
                elif isinstance(output, AgentTextResponse):
                    self.logger.info("Path 2b")
                    return MinimalAgentResponse(
                        message=output.message,
                        actions=[],
                    )
                elif isinstance(output, AgentErrorResponse):
                    self.logger.info("Path 2c")
                    return MinimalAgentResponse(
                        message=f"Error: {output.error}",
                        actions=[],
                    )
                else:
                    # Generic AgentResponse handling
                    self.logger.info("Path 3")
                    return MinimalAgentResponse(
                        message=getattr(output, "message", str(output)),
                        actions=getattr(output, "actions", []),
                    )

            # 3) Dict payload (e.g., already-serialized JSON)
            if isinstance(output, dict) and "message" in output:
                self.logger.info("Path 4")
                return MinimalAgentResponse(
                    message=str(output.get("message", "")),
                    actions=output.get("actions", []),
                )

            self.logger.info("Path 5")
            # 4) Fallback: treat as plain text
            response_message = str(output) if output else "I'm here to help! How can I assist you?"
            return MinimalAgentResponse(message=getattr(output, "message", str(output)), actions=getattr(output, "actions", []))

        except Exception as e:
            self.logger.error(f"Error processing prompt with MCP-enhanced agent: {e}")
            return MinimalAgentResponse(
                message="I encountered an error processing your request. Please try again.",
                actions=[]
            )

    async def cleanup(self) -> None:
        """Cleanup resources."""
        self.logger.info("Cleaning up MCP-enhanced agent resources")

    @classmethod
    def from_config(cls, config: AgentServiceConfig, mcp_server_url: str, lf: LogFactory = NO_LOG_FACTORY) -> "MCPEnhancedAgent":
        """
        Create MCPEnhancedAgent from configuration.
        """
        return cls(
            api_key=config.openai_api_key,
            mcp_server_url=mcp_server_url,
            model_name=config.agent_model_name,
            temperature=config.agent_temperature,
            timeout=config.agent_timeout,
            lf=lf
        )
